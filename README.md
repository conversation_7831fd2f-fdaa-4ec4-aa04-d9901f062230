# ERP-PY: Odoo-like ERP System

A minimal Odoo-like ERP system built in Python with WSGI server, addon system, and model inheritance support.

## Features

- **WSGI Server**: Flask-based server with HTTP API endpoints
- **Addon System**: Odoo-like addon loading with manifest support
- **Model Inheritance**: Base model class with Odoo-style field definitions
- **Multi-database Support**: Configuration for single and multi-database architecture
- **Core Models**: `ir.module.module`, `ir.model`, and `ir.model.fields`
- **Field System**: Comprehensive field types (Char, Text, Integer, Float, Boolean, Date, Datetime, Selection, Many2one, One2many, Many2many)
- **Common Fields**: All models include `id` (UUID), `name`, `created_at`, `updated_at`

## Project Structure

```
erp-py/
├── addons/                 # Addons directory
│   └── base/              # Base addon
│       ├── __manifest__.py
│       ├── __init__.py
│       └── models/
├── config/                # Configuration files
│   └── erp.conf          # Main configuration
├── erp/                   # Core ERP system
│   ├── addons/           # Addon loading system
│   ├── database/         # Database management
│   ├── models/           # Core models
│   ├── config.py         # Configuration management
│   ├── fields.py         # Field definitions
│   └── server.py         # WSGI server
├── venv/                 # Virtual environment
├── requirements.txt      # Python dependencies
├── server.py            # Server entry point
└── test_system.py       # System tests
```

## Installation

1. **Clone and setup virtual environment:**
```bash
cd erp-py
python -m venv venv
venv\Scripts\activate  # Windows
# or
source venv/bin/activate  # Linux/Mac
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Configure the system:**
Edit `config/erp.conf` to match your database settings.

## Usage

### Start the Server

```bash
python server.py
```

Options:
- `--port 8080`: Custom port
- `--host 0.0.0.0`: Custom host
- `--debug`: Enable debug mode
- `--db-name mydb`: Custom database name
- `--config path/to/config.conf`: Custom config file

### Test the System

```bash
python test_system.py
```

### API Endpoints

- `GET /`: Server status
- `GET /addons`: List loaded addons
- `GET /models`: List registered models
- `POST /web/dataset/call_kw`: Call model methods
- `POST /web/dataset/search_read`: Search and read records
- `POST /web/session/authenticate`: User authentication

## Creating Addons

### 1. Create Addon Directory
```bash
mkdir addons/my_addon
```

### 2. Create Manifest
`addons/my_addon/__manifest__.py`:
```python
{
    'name': 'My Addon',
    'version': '1.0.0',
    'description': 'My custom addon',
    'author': 'Your Name',
    'category': 'Custom',
    'depends': ['base'],
    'data': [],
    'installable': True,
    'auto_install': False,
}
```

### 3. Create Models
`addons/my_addon/models/my_model.py`:
```python
import sys
import os

# Add ERP core to path
erp_path = os.path.join(os.path.dirname(__file__), '..', '..', '..')
if erp_path not in sys.path:
    sys.path.insert(0, erp_path)

from erp.models.base import BaseModel
from erp.fields import Char, Text, Integer, Boolean

class MyModel(BaseModel):
    _name = 'my.model'
    _description = 'My Custom Model'
    _table = 'my_model'
    
    # Custom fields (id, name, created_at, updated_at are inherited)
    description = Text(string='Description')
    active = Boolean(string='Active', default=True)
    sequence = Integer(string='Sequence', default=10)
```

## Model System

### Base Model Features
All models inherit from `BaseModel` and automatically get:
- `id`: UUID primary key
- `name`: Required name field
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Field Types
- `Char(size=None)`: Character field
- `Text()`: Long text field
- `Integer()`: Integer field
- `Float(digits=None)`: Float field
- `Boolean()`: Boolean field
- `Date()`: Date field
- `Datetime()`: Datetime field
- `Selection(selection)`: Selection field
- `Many2one(comodel_name)`: Many-to-one relationship
- `One2many(comodel_name, inverse_name)`: One-to-many relationship
- `Many2many(comodel_name)`: Many-to-many relationship

### Field Parameters
- `string`: Field label
- `required`: Required field
- `readonly`: Read-only field
- `default`: Default value (can be callable)
- `help`: Help text
- `index`: Create database index

## Configuration

### Database Configuration
```ini
[options]
db_host = localhost
db_port = 5432
db_user = erp
db_password = erp
db_name = erp_db
```

### Server Configuration
```ini
[options]
http_port = 8069
http_interface = 127.0.0.1
```

### Multi-database Support
```ini
[options]
list_db = True
db_filter = .*
```

## Development

### Running Tests
```bash
python test_system.py
```

### Debug Mode
```bash
python server.py --debug
```

### Adding New Models
1. Create model class inheriting from `BaseModel`
2. Set `_name`, `_description`, and `_table` attributes
3. Define fields using field classes
4. Import in addon's `models/__init__.py`

## Architecture

- **Model Registry**: Automatic model registration via metaclass
- **Addon Loader**: Dependency resolution and loading
- **Database Manager**: Connection pooling and query execution
- **Configuration System**: Centralized configuration management
- **WSGI Application**: RESTful API with JSON responses

## License

This project is for educational purposes and demonstrates Odoo-like ERP architecture patterns.
