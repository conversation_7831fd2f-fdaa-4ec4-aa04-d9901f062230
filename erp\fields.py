"""
Field definitions for ERP models - Odoo-like field system
"""
import uuid
from datetime import datetime
from typing import Any, Optional, Dict, List


class Field:
    """Base field class"""
    
    def __init__(self, string=None, required=False, readonly=False, default=None, 
                 help=None, index=False, **kwargs):
        self.string = string
        self.required = required
        self.readonly = readonly
        self.default = default
        self.help = help
        self.index = index
        self.kwargs = kwargs
        
    def get_default_value(self):
        """Get the default value for this field"""
        if callable(self.default):
            return self.default()
        return self.default


class Char(Field):
    """Character field"""
    
    def __init__(self, size=None, **kwargs):
        super().__init__(**kwargs)
        self.size = size


class Text(Field):
    """Text field for longer content"""
    pass


class Integer(Field):
    """Integer field"""
    pass


class Float(Field):
    """Float field"""
    
    def __init__(self, digits=None, **kwargs):
        super().__init__(**kwargs)
        self.digits = digits


class Boolean(Field):
    """Boolean field"""
    pass


class Date(Field):
    """Date field"""
    pass


class Datetime(Field):
    """Datetime field"""
    
    def __init__(self, **kwargs):
        if 'default' not in kwargs:
            kwargs['default'] = lambda: datetime.now()
        super().__init__(**kwargs)


class Selection(Field):
    """Selection field"""
    
    def __init__(self, selection, **kwargs):
        super().__init__(**kwargs)
        self.selection = selection


class Many2one(Field):
    """Many to one relationship field"""
    
    def __init__(self, comodel_name, **kwargs):
        super().__init__(**kwargs)
        self.comodel_name = comodel_name


class One2many(Field):
    """One to many relationship field"""
    
    def __init__(self, comodel_name, inverse_name, **kwargs):
        super().__init__(**kwargs)
        self.comodel_name = comodel_name
        self.inverse_name = inverse_name


class Many2many(Field):
    """Many to many relationship field"""
    
    def __init__(self, comodel_name, **kwargs):
        super().__init__(**kwargs)
        self.comodel_name = comodel_name
