"""
Configuration management for ERP system
"""
import os
import configparser
from typing import Dict, Any, Optional


class Config:
    """Configuration manager"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config = configparser.ConfigParser()
        self.config_file = config_file or self._find_config_file()
        self._load_config()
    
    def _find_config_file(self) -> str:
        """Find configuration file"""
        possible_paths = [
            'config/erp.conf',
            'erp.conf',
            '/etc/erp/erp.conf',
        ]
        
        for path in possible_paths:
            if os.path.exists(path):
                return path
        
        # Return default path if none found
        return 'config/erp.conf'
    
    def _load_config(self):
        """Load configuration from file"""
        if os.path.exists(self.config_file):
            self.config.read(self.config_file)
        else:
            # Set default values
            self._set_defaults()
    
    def _set_defaults(self):
        """Set default configuration values"""
        self.config.add_section('options')
        self.config.set('options', 'http_port', '8069')
        self.config.set('options', 'http_interface', '127.0.0.1')
        self.config.set('options', 'db_host', 'localhost')
        self.config.set('options', 'db_port', '5432')
        self.config.set('options', 'db_user', 'erp')
        self.config.set('options', 'db_password', 'erp')
        self.config.set('options', 'db_name', 'erp_db')
        self.config.set('options', 'list_db', 'True')
        self.config.set('options', 'db_filter', '.*')
        self.config.set('options', 'addons_path', 'addons')
        self.config.set('options', 'log_level', 'info')
        self.config.set('options', 'admin_passwd', 'admin')
    
    def get(self, section: str, option: str, fallback: Any = None) -> Any:
        """Get configuration value"""
        try:
            return self.config.get(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError):
            return fallback
    
    def getint(self, section: str, option: str, fallback: int = 0) -> int:
        """Get integer configuration value"""
        try:
            return self.config.getint(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def getboolean(self, section: str, option: str, fallback: bool = False) -> bool:
        """Get boolean configuration value"""
        try:
            return self.config.getboolean(section, option)
        except (configparser.NoSectionError, configparser.NoOptionError, ValueError):
            return fallback
    
    def set(self, section: str, option: str, value: str):
        """Set configuration value"""
        if not self.config.has_section(section):
            self.config.add_section(section)
        self.config.set(section, option, value)
    
    def save(self):
        """Save configuration to file"""
        os.makedirs(os.path.dirname(self.config_file), exist_ok=True)
        with open(self.config_file, 'w') as f:
            self.config.write(f)
    
    @property
    def db_config(self) -> Dict[str, Any]:
        """Get database configuration"""
        return {
            'host': self.get('options', 'db_host', 'localhost'),
            'port': self.getint('options', 'db_port', 5432),
            'user': self.get('options', 'db_user', 'erp'),
            'password': self.get('options', 'db_password', 'erp'),
            'database': self.get('options', 'db_name', 'erp_db'),
        }
    
    @property
    def server_config(self) -> Dict[str, Any]:
        """Get server configuration"""
        return {
            'host': self.get('options', 'http_interface', '127.0.0.1'),
            'port': self.getint('options', 'http_port', 8069),
        }
    
    @property
    def addons_path(self) -> str:
        """Get addons path"""
        return self.get('options', 'addons_path', 'addons')
    
    @property
    def list_db(self) -> bool:
        """Check if database listing is enabled"""
        return self.getboolean('options', 'list_db', True)


# Global configuration instance
config = Config()
